<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="231" height="140">
<path d="M0 0 C-1.28231516 2.79447927 -2.65549621 4.88410616 -4.75 7.125 C-12.83727967 16.36194197 -16.5484451 25.47828675 -16.30859375 37.78125 C-15.60617401 45.10775467 -12.41057348 50.48639371 -7.046875 55.4375 C-3.32549281 58.27824976 0.69891169 60.19287608 5 62 C5.69738281 62.29777344 6.39476562 62.59554687 7.11328125 62.90234375 C16.65420096 66.51387833 27.83888504 67.72579393 38 67 C39.74023438 66.88011719 39.74023438 66.88011719 41.515625 66.7578125 C72.32830432 64.11252683 103.65909046 51.98758465 125 29 C127.03366617 26.32902295 128.91621017 23.64645985 130.6953125 20.80078125 C131.34113281 19.90939453 131.34113281 19.90939453 132 19 C132.66 19 133.32 19 134 19 C133.45413544 23.60952295 131.44555383 26.9513791 128.9375 30.75 C128.534104 31.36689697 128.13070801 31.98379395 127.71508789 32.61938477 C122.20779885 40.87762131 115.68953567 47.7465406 108 54 C107.06542969 54.77214844 106.13085938 55.54429688 105.16796875 56.33984375 C86.48704711 71.44889308 64.16607125 81.09568 41 87 C39.9006311 87.2947522 39.9006311 87.2947522 38.77905273 87.59545898 C31.36163379 89.35532555 23.90186643 89.33788817 16.32348633 89.31567383 C14.06998399 89.31251051 11.81779448 89.3360593 9.56445312 89.36132812 C-4.72528069 89.42523834 -19.09237441 86.73277058 -30 76.6875 C-36.26831351 69.8076437 -38.40225415 61.20657934 -38.26953125 52.12109375 C-37.30556131 40.95860121 -32.18885816 32.03547875 -26 23 C-25.46503906 22.21753906 -24.93007813 21.43507813 -24.37890625 20.62890625 C-23.92386719 20.09136719 -23.46882813 19.55382812 -23 19 C-22.34 19 -21.68 19 -21 19 C-20.63261719 18.14535156 -20.63261719 18.14535156 -20.2578125 17.2734375 C-17.13335558 11.62612716 -6.98623695 0 0 0 Z " fill="#890000" transform="translate(67,51)"/>
<path d="M0 0 C6.24291037 0.3901819 10.77003235 3.49510384 16 6.625 C23.19023952 10.83893911 30.38434329 14.78001623 37.87890625 18.42578125 C41.00214176 20.00108026 43.99471449 21.71104555 47 23.5 C51.17264473 25.9837171 55.39098259 28.23493942 59.75 30.375 C66.95823955 33.92367178 73.92665613 37.82881902 80.90185547 41.81201172 C83.10420938 43.05900429 85.31787641 44.28366782 87.53515625 45.50390625 C88.2720166 45.91141113 89.00887695 46.31891602 89.76806641 46.73876953 C91.18868921 47.52374117 92.61147095 48.30482122 94.03662109 49.08154297 C98.88776567 51.77553133 98.88776567 51.77553133 100 54 C95.38 54 90.76 54 86 54 C86.03665771 54.99854004 86.07331543 55.99708008 86.11108398 57.02587891 C86.23828188 60.7490607 86.31831929 64.47205294 86.38452148 68.19677734 C86.4196133 69.8051793 86.46730247 71.41335968 86.52807617 73.02099609 C87.01507532 86.23954431 87.01507532 86.23954431 84.70068359 90.25341797 C82.88593647 91.8725636 81.17129878 92.9119069 79 94 C78.330896 94.41942871 77.66179199 94.83885742 76.97241211 95.27099609 C74.93292883 96.5477675 72.85936173 97.71799935 70.75 98.875 C69.69039062 99.47828125 69.69039062 99.47828125 68.609375 100.09375 C63.38998724 103 63.38998724 103 60 103 C60.04782623 102.12581924 60.04782623 102.12581924 60.09661865 101.23397827 C60.41177857 95.10059044 60.5813903 88.97471102 60.67138672 82.83398438 C60.72325127 80.55194392 60.81072916 78.27039873 60.93603516 75.99121094 C61.89973584 63.17920503 61.89973584 63.17920503 57.21276855 51.70620728 C52.54458495 47.43128629 47.20397053 44.40557878 41.63952637 41.44543457 C37.74206382 39.31113988 34.17146213 36.7758859 30.546875 34.21484375 C29.23789065 33.32291457 27.92671596 32.43419073 26.61328125 31.54882812 C23.98913376 29.7753512 21.36680776 27.99925008 18.74609375 26.22070312 C18.09680908 25.78136948 17.44752441 25.34203583 16.77856445 24.88938904 C15.52085804 24.03775532 14.26477146 23.18372337 13.01049805 22.32704163 C9.78938502 20.14143327 6.53373325 18.18084076 3.109375 16.3359375 C0 14 0 14 -0.78125 10.44921875 C-0.6884375 9.20785156 -0.595625 7.96648438 -0.5 6.6875 C-0.4278125 5.43324219 -0.355625 4.17898437 -0.28125 2.88671875 C-0.1884375 1.93410156 -0.095625 0.98148437 0 0 Z " fill="#890000" transform="translate(94,0)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19 6.27 19 12.54 19 19 C12.73 19 6.46 19 0 19 C0 12.73 0 6.46 0 0 Z " fill="#8A0000" transform="translate(83,78)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19 6.27 19 12.54 19 19 C12.73 19 6.46 19 0 19 C0 12.73 0 6.46 0 0 Z " fill="#8A0000" transform="translate(83,52)"/>
<path d="M0 0 C5.94 0 11.88 0 18 0 C18 6.27 18 12.54 18 19 C12.06 19 6.12 19 0 19 C0 12.73 0 6.46 0 0 Z " fill="#8A0000" transform="translate(110,78)"/>
<path d="M0 0 C5.94 0 11.88 0 18 0 C18 6.27 18 12.54 18 19 C12.06 19 6.12 19 0 19 C0 12.73 0 6.46 0 0 Z " fill="#8A0000" transform="translate(110,52)"/>
</svg>
